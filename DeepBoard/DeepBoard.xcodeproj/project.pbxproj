// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		8E652E162E50192B004EAFA3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E652DFA2E501926004EAFA3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E652E012E501926004EAFA3;
			remoteInfo = DeepBoard;
		};
		8E652E202E50192B004EAFA3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E652DFA2E501926004EAFA3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E652E012E501926004EAFA3;
			remoteInfo = DeepBoard;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8E652E022E501926004EAFA3 /* DeepBoard.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DeepBoard.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8E652E152E50192B004EAFA3 /* DeepBoardTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DeepBoardTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8E652E1F2E50192B004EAFA3 /* DeepBoardUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DeepBoardUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8E652E042E501926004EAFA3 /* DeepBoard */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DeepBoard;
			sourceTree = "<group>";
		};
		8E652E182E50192B004EAFA3 /* DeepBoardTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DeepBoardTests;
			sourceTree = "<group>";
		};
		8E652E222E50192B004EAFA3 /* DeepBoardUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DeepBoardUITests;
			sourceTree = "<group>";
		};
		8E652E332E50193E004EAFA3 /* Packages */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Packages;
			sourceTree = "<group>";
		};
		8E652E342E501950004EAFA3 /* ThirdParty */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ThirdParty;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8E652DFF2E501926004EAFA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E652E122E50192B004EAFA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E652E1C2E50192B004EAFA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8E652DF92E501926004EAFA3 = {
			isa = PBXGroup;
			children = (
				8E652E342E501950004EAFA3 /* ThirdParty */,
				8E652E332E50193E004EAFA3 /* Packages */,
				8E652E042E501926004EAFA3 /* DeepBoard */,
				8E652E182E50192B004EAFA3 /* DeepBoardTests */,
				8E652E222E50192B004EAFA3 /* DeepBoardUITests */,
				8E652E032E501926004EAFA3 /* Products */,
			);
			sourceTree = "<group>";
		};
		8E652E032E501926004EAFA3 /* Products */ = {
			isa = PBXGroup;
			children = (
				8E652E022E501926004EAFA3 /* DeepBoard.app */,
				8E652E152E50192B004EAFA3 /* DeepBoardTests.xctest */,
				8E652E1F2E50192B004EAFA3 /* DeepBoardUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8E652E012E501926004EAFA3 /* DeepBoard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E652E292E50192B004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoard" */;
			buildPhases = (
				8E652DFE2E501926004EAFA3 /* Sources */,
				8E652DFF2E501926004EAFA3 /* Frameworks */,
				8E652E002E501926004EAFA3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8E652E042E501926004EAFA3 /* DeepBoard */,
			);
			name = DeepBoard;
			packageProductDependencies = (
			);
			productName = DeepBoard;
			productReference = 8E652E022E501926004EAFA3 /* DeepBoard.app */;
			productType = "com.apple.product-type.application";
		};
		8E652E142E50192B004EAFA3 /* DeepBoardTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E652E2C2E50192B004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardTests" */;
			buildPhases = (
				8E652E112E50192B004EAFA3 /* Sources */,
				8E652E122E50192B004EAFA3 /* Frameworks */,
				8E652E132E50192B004EAFA3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E652E172E50192B004EAFA3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E652E182E50192B004EAFA3 /* DeepBoardTests */,
			);
			name = DeepBoardTests;
			packageProductDependencies = (
			);
			productName = DeepBoardTests;
			productReference = 8E652E152E50192B004EAFA3 /* DeepBoardTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8E652E1E2E50192B004EAFA3 /* DeepBoardUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E652E2F2E50192B004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardUITests" */;
			buildPhases = (
				8E652E1B2E50192B004EAFA3 /* Sources */,
				8E652E1C2E50192B004EAFA3 /* Frameworks */,
				8E652E1D2E50192B004EAFA3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E652E212E50192B004EAFA3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E652E222E50192B004EAFA3 /* DeepBoardUITests */,
				8E652E332E50193E004EAFA3 /* Packages */,
				8E652E342E501950004EAFA3 /* ThirdParty */,
			);
			name = DeepBoardUITests;
			packageProductDependencies = (
			);
			productName = DeepBoardUITests;
			productReference = 8E652E1F2E50192B004EAFA3 /* DeepBoardUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8E652DFA2E501926004EAFA3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					8E652E012E501926004EAFA3 = {
						CreatedOnToolsVersion = 16.2;
					};
					8E652E142E50192B004EAFA3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E652E012E501926004EAFA3;
					};
					8E652E1E2E50192B004EAFA3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E652E012E501926004EAFA3;
					};
				};
			};
			buildConfigurationList = 8E652DFD2E501926004EAFA3 /* Build configuration list for PBXProject "DeepBoard" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8E652DF92E501926004EAFA3;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8E652E032E501926004EAFA3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8E652E012E501926004EAFA3 /* DeepBoard */,
				8E652E142E50192B004EAFA3 /* DeepBoardTests */,
				8E652E1E2E50192B004EAFA3 /* DeepBoardUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8E652E002E501926004EAFA3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E652E132E50192B004EAFA3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E652E1D2E50192B004EAFA3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8E652DFE2E501926004EAFA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E652E112E50192B004EAFA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E652E1B2E50192B004EAFA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8E652E172E50192B004EAFA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E652E012E501926004EAFA3 /* DeepBoard */;
			targetProxy = 8E652E162E50192B004EAFA3 /* PBXContainerItemProxy */;
		};
		8E652E212E50192B004EAFA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E652E012E501926004EAFA3 /* DeepBoard */;
			targetProxy = 8E652E202E50192B004EAFA3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8E652E272E50192B004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8E652E282E50192B004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8E652E2A2E50192B004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DeepBoard/DeepBoard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DeepBoard/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8E652E2B2E50192B004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DeepBoard/DeepBoard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DeepBoard/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		8E652E2D2E50192B004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DeepBoard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DeepBoard";
			};
			name = Debug;
		};
		8E652E2E2E50192B004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DeepBoard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DeepBoard";
			};
			name = Release;
		};
		8E652E302E50192B004EAFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = DeepBoard;
			};
			name = Debug;
		};
		8E652E312E50192B004EAFA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.DeepBoardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = DeepBoard;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8E652DFD2E501926004EAFA3 /* Build configuration list for PBXProject "DeepBoard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E652E272E50192B004EAFA3 /* Debug */,
				8E652E282E50192B004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E652E292E50192B004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E652E2A2E50192B004EAFA3 /* Debug */,
				8E652E2B2E50192B004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E652E2C2E50192B004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E652E2D2E50192B004EAFA3 /* Debug */,
				8E652E2E2E50192B004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E652E2F2E50192B004EAFA3 /* Build configuration list for PBXNativeTarget "DeepBoardUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E652E302E50192B004EAFA3 /* Debug */,
				8E652E312E50192B004EAFA3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8E652DFA2E501926004EAFA3 /* Project object */;
}
