<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>runDestinationsByUUID</key>
	<dict>
		<key>9F80FB9B-4E37-45A8-BFE8-9AF36737A6F3</key>
		<dict>
			<key>localComputer</key>
			<dict>
				<key>busSpeedInMHz</key>
				<integer>100</integer>
				<key>cpuCount</key>
				<integer>1</integer>
				<key>cpuKind</key>
				<string>Intel Core i7</string>
				<key>cpuSpeedInMHz</key>
				<integer>3100</integer>
				<key>logicalCPUCoresPerPackage</key>
				<integer>4</integer>
				<key>modelCode</key>
				<string>MacBookPro12,1</string>
				<key>physicalCPUCoresPerPackage</key>
				<integer>2</integer>
				<key>platformIdentifier</key>
				<string>com.apple.platform.macosx</string>
			</dict>
			<key>targetArchitecture</key>
			<string>x86_64</string>
			<key>targetDevice</key>
			<dict>
				<key>modelCode</key>
				<string>iPhone7,2</string>
				<key>platformIdentifier</key>
				<string>com.apple.platform.iphonesimulator</string>
			</dict>
		</dict>
		<key>F9553B46-8F24-4C2B-8A1E-8CC5535D12E1</key>
		<dict>
			<key>localComputer</key>
			<dict>
				<key>busSpeedInMHz</key>
				<integer>100</integer>
				<key>cpuCount</key>
				<integer>1</integer>
				<key>cpuKind</key>
				<string>Intel Core i7</string>
				<key>cpuSpeedInMHz</key>
				<integer>3100</integer>
				<key>logicalCPUCoresPerPackage</key>
				<integer>4</integer>
				<key>modelCode</key>
				<string>MacBookPro12,1</string>
				<key>physicalCPUCoresPerPackage</key>
				<integer>2</integer>
				<key>platformIdentifier</key>
				<string>com.apple.platform.macosx</string>
			</dict>
			<key>targetArchitecture</key>
			<string>i386</string>
			<key>targetDevice</key>
			<dict>
				<key>modelCode</key>
				<string>iPhone5,1</string>
				<key>platformIdentifier</key>
				<string>com.apple.platform.iphonesimulator</string>
			</dict>
		</dict>
	</dict>
</dict>
</plist>
