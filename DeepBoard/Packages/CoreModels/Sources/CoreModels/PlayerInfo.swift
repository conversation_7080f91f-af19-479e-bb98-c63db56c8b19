import Foundation

/// Player information with FIDE data integration
public struct PlayerInfo: Codable, Equatable, Sendable {
    public let fideId: String?
    public let name: String
    public let rating: Int?
    public let federation: String?
    public let title: String?
    public let photoData: Data?
    public let photoBase64: String?
    public let cacheTimestamp: Date?
    
    public init(
        fideId: String? = nil,
        name: String,
        rating: Int? = nil,
        federation: String? = nil,
        title: String? = nil,
        photoData: Data? = nil,
        photoBase64: String? = nil,
        cacheTimestamp: Date? = nil
    ) {
        self.fideId = fideId
        self.name = name
        self.rating = rating
        self.federation = federation
        self.title = title
        self.photoData = photoData
        self.photoBase64 = photoBase64
        self.cacheTimestamp = cacheTimestamp
    }
    
    /// Whether the cached data is still valid (24 hours)
    public var isCacheValid: Bool {
        guard let timestamp = cacheTimestamp else { return false }
        return Date().timeIntervalSince(timestamp) < 24 * 3600
    }
    
    /// Whether this player has FIDE data
    public var hasFIDEData: Bool {
        return fideId != nil && rating != nil
    }
    
    /// Display name with title if available
    public var displayName: String {
        if let title = title, !title.isEmpty {
            return "\(title) \(name)"
        }
        return name
    }
    
    /// Full player description with rating and federation
    public var fullDescription: String {
        var components: [String] = [displayName]
        
        if let rating = rating {
            components.append("(\(rating))")
        }
        
        if let federation = federation, !federation.isEmpty {
            components.append(federation)
        }
        
        return components.joined(separator: " ")
    }
}

/// FIDE data cache entry
public struct FIDEPlayerData: Codable, Equatable, Sendable {
    public let fideId: String
    public let name: String
    public let rating: Int?
    public let federation: String?
    public let title: String?
    public let photoData: Data?
    public let photoBase64: String?
    public let cacheTimestamp: Date
    
    public init(
        fideId: String,
        name: String,
        rating: Int? = nil,
        federation: String? = nil,
        title: String? = nil,
        photoData: Data? = nil,
        photoBase64: String? = nil,
        cacheTimestamp: Date = Date()
    ) {
        self.fideId = fideId
        self.name = name
        self.rating = rating
        self.federation = federation
        self.title = title
        self.photoData = photoData
        self.photoBase64 = photoBase64
        self.cacheTimestamp = cacheTimestamp
    }
    
    /// Convert to PlayerInfo
    public func toPlayerInfo() -> PlayerInfo {
        return PlayerInfo(
            fideId: fideId,
            name: name,
            rating: rating,
            federation: federation,
            title: title,
            photoData: photoData,
            photoBase64: photoBase64,
            cacheTimestamp: cacheTimestamp
        )
    }
    
    /// Whether the cached data is still valid (24 hours)
    public var isExpired: Bool {
        return Date().timeIntervalSince(cacheTimestamp) >= 24 * 3600
    }
}