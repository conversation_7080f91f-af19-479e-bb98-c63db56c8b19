import Foundation

/// Represents the current status of a chess game
public enum GameStatus: Equatable, Sendable {
    case inProgress
    case checkmate(WinningColor)
    case stalemate  
    case draw(DrawType)
    
    /// The winning color in a checkmate situation
    public enum WinningColor: Equatable, Sendable {
        case white
        case black
    }
    
    /// Types of draw outcomes
    public enum DrawType: Equatable, Sendable {
        case insufficientMaterial
        case threefoldRepetition
        case fiftyMoveRule
        case agreement
        case timeout
        case other(String)
    }
    
    /// Whether the game has ended
    public var isGameOver: Bool {
        switch self {
        case .inProgress:
            return false
        case .checkmate, .stalemate, .draw:
            return true
        }
    }
    
    /// Human-readable description of the game status
    public var description: String {
        switch self {
        case .inProgress:
            return "Game in progress"
        case .checkmate(let winner):
            return "\(winner == .white ? "White" : "Black") wins by checkmate"
        case .stalemate:
            return "Draw by stalemate"
        case .draw(let type):
            return "Draw by \(type.description)"
        }
    }
}

extension GameStatus.DrawType {
    public var description: String {
        switch self {
        case .insufficientMaterial:
            return "insufficient material"
        case .threefoldRepetition:
            return "threefold repetition"
        case .fiftyMoveRule:
            return "fifty-move rule"
        case .agreement:
            return "agreement"
        case .timeout:
            return "timeout"
        case .other(let reason):
            return reason
        }
    }
}