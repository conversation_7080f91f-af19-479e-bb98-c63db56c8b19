/// CoreModels - Foundation domain models for DeepBoard chess application
/// 
/// This package provides the core domain models, protocols, and data structures
/// that are independent of UI frameworks and external chess libraries.
/// 
/// Key components:
/// - GameSessionInfo: Core game session representation
/// - GameStatus: Domain-specific game state enumeration  
/// - PlayerInfo: Player data with FIDE integration
/// - UI State models: DragState, AnnotationState, etc.
/// - Service protocols: GameSessionManaging, ChessGameState, etc.

// Re-export all public types for convenience
@_exported import Foundation

public typealias CoreModelsVersion = String
public let coreModelsVersion: CoreModelsVersion = "1.0.0"