import Foundation

/// Core domain model representing a chess game session
public struct GameSessionInfo: Identifiable, Sendable {
    public let id: UUID
    public var name: String
    public var isActive: Bool
    public var isModified: Bool
    public var isBoardFlipped: Bool
    public var currentFilePath: URL?
    public var gameStatus: GameStatus
    public var whitePlayer: PlayerInfo?
    public var blackPlayer: PlayerInfo?
    public var event: String?
    public var site: String?
    public var date: String?
    public var round: String?
    public var result: String?
    
    public init(
        id: UUID = UUID(),
        name: String = "New Game",
        isActive: Bool = false,
        isModified: Bool = false,
        isBoardFlipped: Bool = false,
        currentFilePath: URL? = nil,
        gameStatus: GameStatus = .inProgress,
        whitePlayer: PlayerInfo? = nil,
        blackPlayer: PlayerInfo? = nil,
        event: String? = nil,
        site: String? = nil,
        date: String? = nil,
        round: String? = nil,
        result: String? = nil
    ) {
        self.id = id
        self.name = name
        self.isActive = isActive
        self.isModified = isModified
        self.isBoardFlipped = isBoardFlipped
        self.currentFilePath = currentFilePath
        self.gameStatus = gameStatus
        self.whitePlayer = whitePlayer
        self.blackPlayer = blackPlayer
        self.event = event
        self.site = site
        self.date = date
        self.round = round
        self.result = result
    }
    
    /// Generate a descriptive name for the game
    public var generatedName: String {
        var components: [String] = []
        
        // Try to use player names
        if let white = whitePlayer?.name, let black = blackPlayer?.name {
            components.append("\(white) vs \(black)")
        }
        
        // Add event if available and doesn't duplicate player information
        if let event = event, !event.isEmpty {
            let playerNames = [whitePlayer?.name, blackPlayer?.name].compactMap { $0 }
            let eventContainsPlayerName = playerNames.contains { playerName in
                event.contains(playerName)
            }
            
            if !eventContainsPlayerName {
                if components.isEmpty {
                    components.append(event)
                } else {
                    components.append("(\(event))")
                }
            }
        }
        
        // Add date if available
        if let date = date, !date.isEmpty {
            components.append(date)
        }
        
        return components.isEmpty ? "New Game" : components.joined(separator: " ")
    }
    
    /// Whether the session has unsaved changes
    public var hasUnsavedChanges: Bool {
        return isModified && currentFilePath == nil
    }
    
    /// Display name for UI
    public var displayName: String {
        return name.isEmpty ? generatedName : name
    }
    
    /// Full file name including path
    public var fileName: String? {
        return currentFilePath?.lastPathComponent
    }
}

/// Domain events for game session changes
public enum GameSessionEvent: Sendable {
    case sessionCreated(GameSessionInfo)
    case sessionUpdated(GameSessionInfo)
    case sessionRemoved(UUID)
    case activeSessionChanged(UUID?)
    case sessionModified(UUID, Bool)
    case sessionRenamed(UUID, String)
    case gameStatusChanged(UUID, GameStatus)
}

/// Result type for session operations
public enum GameSessionError: Error, LocalizedError, Sendable, Equatable {
    case sessionNotFound(UUID)
    case sessionAlreadyActive(UUID)
    case cannotRemoveActiveSession
    case invalidGameData(String)
    case fileOperationFailed(String)
    
    public var errorDescription: String? {
        switch self {
        case .sessionNotFound(let id):
            return "Session with ID \(id) not found"
        case .sessionAlreadyActive(let id):
            return "Session with ID \(id) is already active"
        case .cannotRemoveActiveSession:
            return "Cannot remove the currently active session"
        case .invalidGameData(let message):
            return "Invalid game data: \(message)"
        case .fileOperationFailed(let message):
            return "File operation failed: \(message)"
        }
    }
}