import Foundation

/// Core chess move representation
public protocol ChessMove: Sendable {
    var startSquare: String { get }
    var endSquare: String { get }
    var promotionPiece: String? { get }
    var notation: String { get }
    var isCapture: Bool { get }
    var isCheck: Bool { get }
    var isCheckmate: Bo<PERSON> { get }
}

/// Chess position representation
public protocol ChessPosition: Sendable {
    var fen: String { get }
    var isGameOver: Bool { get }
    var gameStatus: GameStatus { get }
    var legalMoves: [ChessMove] { get }
    var currentTurn: PlayerColor { get }
}

/// Player color enumeration
public enum PlayerColor: String, CaseIterable, Sendable {
    case white = "white"
    case black = "black"
    
    public var opposite: PlayerColor {
        return self == .white ? .black : .white
    }
}

/// Chess game state management
public protocol ChessGameState: Sendable {
    /// Current position in the game
    var currentPosition: ChessPosition { get async }
    
    /// Game metadata
    var gameInfo: GameSessionInfo { get async }
    
    /// Make a move
    func makeMove(from: String, to: String, promotion: String?) async throws -> Bool
    
    /// Undo last move
    func undoMove() async throws -> Bool
    
    /// Redo move
    func redoMove() async throws -> Bool
    
    /// Navigate to specific move
    func goToMove(at index: Int) async throws
    
    /// Get move history
    func getMoveHistory() async -> [ChessMove]
    
    /// Check if move is legal
    func isMoveLegal(from: String, to: String, promotion: String?) async -> Bool
    
    /// Get legal moves for a square
    func getLegalMoves(for square: String) async -> [ChessMove]
}

/// Chess engine analysis protocol
public protocol ChessEngineAnalyzing: Sendable {
    /// Start analysis for a position
    func startAnalysis(position: ChessPosition, depth: Int?) async throws
    
    /// Stop current analysis
    func stopAnalysis() async
    
    /// Get current analysis results
    func getAnalysisResults() async -> EngineAnalysisResult?
    
    /// Set engine configuration
    func configure(settings: EngineSettings) async throws
}

/// Engine analysis result
public struct EngineAnalysisResult: Sendable {
    public let depth: Int
    public let evaluation: EngineEvaluation
    public let principalVariation: [ChessMove]
    public let analysisTime: TimeInterval
    public let nodesSearched: Int?
    
    public init(
        depth: Int,
        evaluation: EngineEvaluation,
        principalVariation: [ChessMove],
        analysisTime: TimeInterval,
        nodesSearched: Int? = nil
    ) {
        self.depth = depth
        self.evaluation = evaluation
        self.principalVariation = principalVariation
        self.analysisTime = analysisTime
        self.nodesSearched = nodesSearched
    }
}

/// Engine evaluation
public enum EngineEvaluation: Sendable, Equatable {
    case centipawns(Int)
    case mate(Int)
    case unknown
    
    public var displayValue: String {
        switch self {
        case .centipawns(let cp):
            return String(format: "%.2f", Double(cp) / 100.0)
        case .mate(let moves):
            return "M\(moves)"
        case .unknown:
            return "?"
        }
    }
}

/// Engine configuration
public struct EngineSettings: Sendable {
    public let depth: Int?
    public let timeLimit: TimeInterval?
    public let threads: Int?
    public let hashSize: Int?
    public let multiPV: Int?
    
    public init(
        depth: Int? = nil,
        timeLimit: TimeInterval? = nil,
        threads: Int? = nil,
        hashSize: Int? = nil,
        multiPV: Int? = nil
    ) {
        self.depth = depth
        self.timeLimit = timeLimit
        self.threads = threads
        self.hashSize = hashSize
        self.multiPV = multiPV
    }
}