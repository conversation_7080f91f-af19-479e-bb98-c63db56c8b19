import Foundation

/// Protocol for managing multiple game sessions
public protocol GameSessionManaging: AnyObject, Sendable {
    /// All current game sessions
    var sessions: [GameSessionInfo] { get async }
    
    /// Currently active session
    var activeSession: GameSessionInfo? { get async }
    
    /// Create a new game session
    func createNewSession(name: String?) async throws -> GameSessionInfo
    
    /// Set the active session
    func setActiveSession(_ sessionId: UUID) async throws
    
    /// Remove a session
    func removeSession(_ sessionId: UUID) async throws
    
    /// Update session information
    func updateSession(_ session: GameSessionInfo) async throws
    
    /// Mark session as modified
    func markSessionModified(_ sessionId: UUID, modified: Bool) async throws
    
    /// Rename a session
    func renameSession(_ sessionId: UUID, name: String) async throws
    
    /// Get session by ID
    func getSession(_ sessionId: UUID) async throws -> GameSessionInfo
}

/// Protocol for game import/export operations
public protocol GameImportExporting: Sendable {
    /// Import PGN content and create sessions
    func importPGN(_ pgn: String) async throws -> [GameSessionInfo]
    
    /// Load a game from file into a new session
    func loadGameInNewSession(from url: URL) async throws -> GameSessionInfo
    
    /// Import from clipboard
    func importFromClipboard() async throws -> GameSessionInfo?
    
    /// Export session to PGN
    func exportSessionToPGN(_ sessionId: UUID) async throws -> String
    
    /// Save session to file
    func saveSession(_ sessionId: UUID, to url: URL) async throws
}

/// Protocol for FIDE data management
public protocol FIDEDataManaging: Sendable {
    /// Get cached FIDE data for a player
    func getCachedFIDEData(for fideId: String) async -> FIDEPlayerData?
    
    /// Fetch FIDE data from network if needed
    func fetchFIDEDataIfNeeded(for fideId: String) async throws -> FIDEPlayerData?
    
    /// Cache FIDE data
    func cacheFIDEData(_ data: FIDEPlayerData) async throws
    
    /// Clear all cached FIDE data
    func clearFIDEDataCache() async throws
    
    /// Clean up expired cache entries
    func cleanupExpiredFIDEData() async throws
    
    /// Check if data exists in cache
    func hasCachedData(for fideId: String) async -> Bool
}