
# ChessKit-Swift Greenfield Refactor Plan

## 1. 总体目标 (Overall Goal)

`chesskit-swift` 库目前功能强大，但随着特性增多，其内部结构变得复杂且耦合度较高。本次“绿地重构”旨在不改变其公开API行为的前提下，优化内部架构，使其更清晰、模块化、可扩展和易于维护。

核心目标：
- **清晰的责任划分 (Clear Separation of Concerns)**: 每个组件只做一件事，例如，解析器只负责解析，棋盘逻辑只负责棋盘状态和规则。
- **模块化设计 (Modular Design)**: 将相关功能组织到独立的模块/目录中。
- **提升可测试性 (Improve Testability)**: 通过协议和依赖注入，使各模块可以独立测试。
- **数据与逻辑分离 (Data-Logic Separation)**: 将核心数据结构（如`Piece`, `Square`）与操作它们的逻辑（如`MoveGenerator`）分离开。
- **拥抱Swift特性 (Embrace Swift Features)**: 更多地使用`struct`和`protocol`，利用协议扩展提供默认实现。

## 2. 现有结构分析 (Analysis of Current Structure)

当前结构的主要问题：
- **`Board.swift`过于庞大**: `Board`结构体承担了过多的责任，包括：
    - 棋盘状态管理 (`position`)
    - 移动合法性验证 (`canMove`, `legalMoves`)
    - 移动执行 (`move`, `castle`)
    - 特殊规则处理（吃过路兵、王车易位）
    - 走法生成 (`pawnAttacks`, `kingMoves`, etc.)
    - 检查/将死/僵局判断 (`checkState`)
- **解析逻辑分散**: FEN, PGN, SAN, LAN 的解析器虽然在`Parsers`目录中，但它们与`Game`, `Board`, `Position`等核心对象的交互可以更清晰。
- **Bitboard中心化**: `Bitboard`是性能优化的核心，但其攻击生成逻辑（`Attacks.swift`）和数据结构（`PieceSet.swift`）与核心棋局逻辑紧密耦合，可以封装得更好。
- **`Game.swift`与`MoveTree.swift`紧密耦合**: `Game`结构体直接管理`MoveTree`和`positions`字典，负责了游戏历史、状态和PGN元数据的全部内容。`MoveTree`本身也包含了大量的编辑逻辑。

## 3. 建议的重构架构与目录结构 (Proposed Refactor Architecture & Directory Structure)

我建议采用分层和按功能划分的模块化结构。新的`Sources/ChessKit`目录结构如下：

```
Sources/ChessKit/
├── Core/
│   ├── Piece.swift           # (Enum: Kind, Color) - 纯数据
│   ├── Square.swift          # (Enum: File, Rank) - 纯数据
│   ├── Move.swift            # (Struct: Move) - 包含起止、吃子、升变等信息
│   └── Castling.swift        # (Struct: Castling) - 王车易位相关数据
│
├── Board/
│   ├── BoardState.swift      # (Struct: BoardState) - 核心棋盘状态，只包含Piece和Square的映射
│   ├── Position.swift        # (Struct: Position) - 包含BoardState, sideToMove, castling rights, en passant square, clocks
│   ├── MoveGenerator.swift   # (Struct) - 负责生成所有伪合法走法 (pseudo-legal moves)
│   └── MoveValidator.swift   # (Struct) - 负责验证一个走法是否合法（处理将军等情况）
│
├── Game/
│   ├── Game.swift            # (Class) - 顶层对象，管理游戏进程
│   ├── MoveTree.swift        # (Struct) - 纯粹的走法树数据结构
│   ├── MoveManager.swift     # (Class) - 负责在MoveTree上执行、撤销、导航走法，并更新Position
│   └── PGN.swift             # (Struct: PGN) - PGN标签和结果
│
├── IO/
│   ├── FEN.swift             # FEN的序列化和反序列化
│   ├── SAN.swift             # SAN的序列化和反序列化
│   ├── PGNSerializer.swift   # 将Game对象转换为PGN字符串
│   └── PGNParser.swift       # 将PGN字符串解析为Game对象
│
├── Performance/
│   ├── Bitboard.swift        # Bitboard的底层实现和操作
│   └── AttackTables.swift    # (前Attacks.swift) - 预计算的攻击表生成和访问
│
└── Protocols/
    ├── ChessEngine.swift     # 定义与外部引擎交互的协议 (如果需要)
    └── BoardRepresentable.swift # 定义棋盘表示的协议
```

## 4. 详细重构计划 (Detailed Refactoring Plan)

### 第1步: 建立核心数据模型 (Phase 1: Establish Core Data Models)

- **目标**: 创建一组纯粹的、不可变的数据模型。
- **操作**:
    1.  迁移`Piece.swift`, `Square.swift`到`Core/`目录。确保它们是简单的`enum`和`struct`，不包含任何游戏逻辑。
    2.  创建`Core/Move.swift`。这个`struct`应该包含`start: Square`, `end: Square`, `promotion: Piece.Kind?`, `capturedPiece: Piece?`等信息。将`MetaMove`和`Move`的概念合并为一个清晰的`Move`结构。
    3.  迁移`Special Moves/Castling.swift`到`Core/`，并简化。

### 第2步: 重构棋盘逻辑 (Phase 2: Refactor Board Logic)

- **目标**: 将`Board.swift`的巨大职责拆分。
- **操作**:
    1.  在`Board/`下创建`BoardState.swift`。它应该只包含棋盘上棋子的布局信息，例如使用一个`[Square: Piece]`字典或一个`Piece`数组。这是最核心的棋子位置快照。
    2.  重构`Position.swift`。它将包含一个`BoardState`实例，以及`sideToMove`, `castlingRights`, `enPassantSquare`, `halfmoveClock`, `fullmoveNumber`。`Position`代表了一个完整的、可以生成FEN的状态。
    3.  创建`Performance/Bitboard.swift`和`Performance/AttackTables.swift`。将所有位棋盘（Bitboard）的实现、`PieceSet`以及`Attacks`的逻辑移入其中。`AttackTables`将成为一个内部实现细节，由`MoveGenerator`使用。
    4.  创建`Board/MoveGenerator.swift`。这个组件接收一个`Position`，并使用`AttackTables`来高效地生成所有伪合法走法（不考虑是否会导致国王被将军）。
    5.  创建`Board/MoveValidator.swift`。这个组件接收一个`Position`和一个`Move`，判断该`Move`是否合法。它的核心职责是：检查执行该`Move`后，自己的国王是否处于被攻击状态。

### 第3步: 重构游戏和走法树 (Phase 3: Refactor Game and Move Tree)

- **目标**: 分离游戏流程控制、历史记录和状态管理。
- **操作**:
    1.  简化`MoveTree.swift`，使其成为一个纯粹的树形数据结构，只负责节点的增、删、查和遍历。所有与`Position`相关的逻辑都应移除。
    2.  创建`Game/MoveManager.swift`。这是一个新的核心组件，它将持有`MoveTree`和`[MoveIndex: Position]`字典。它提供`makeMove`, `undoMove`, `goToMove(index:)`等方法。当一个走法被执行时，`MoveManager`会：
        - 更新`MoveTree`。
        - 基于当前`Position`和`Move`计算出新的`Position`。
        - 将新的`Position`存入字典。
    3.  重构`Game.swift`。它将成为一个更高层次的封装，持有一个`MoveManager`实例。它将处理PGN元数据（`Tags`），并提供更友好的API给库的使用者。

### 第4p步: 统一输入输出 (Phase 4: Unify Input/Output)

- **目标**: 将所有字符串表示的转换逻辑集中到`IO/`模块。
- **操作**:
    1.  创建`IO/FEN.swift`，负责`Position`与FEN字符串之间的相互转换。
    2.  创建`IO/SAN.swift`，负责在给定`Position`的情况下，将`Move`转换为SAN字符串，或将SAN字符串解析为`Move`。
    3.  创建`IO/PGNParser.swift`和`IO/PGNSerializer.swift`。
        - `PGNParser`接收一个PGN字符串，并使用`SAN.swift`和`FEN.swift`来逐步构建一个`Game`对象。
        - `PGNSerializer`接收一个`Game`对象，并将其转换为PGN字符串。

### 第5步: 完善API和文档 (Phase 5: Finalize API and Documentation)

- **目标**: 提供一个干净、一致的公共API，并更新所有文档。
- **操作**:
    1.  审查所有`public`访问控制符，确保只有预期的API是公开的。内部实现细节（如`Bitboard`, `AttackTables`, `MoveGenerator`）应设为`internal`。
    2.  为所有公共类型和方法编写或更新文档注释。
    3.  创建一个迁移指南，帮助现有用户适应新的API（如果公开API有任何微小变动）。

## 5. 风险与应对 (Risks and Mitigation)

- **性能**: Bitboard的重构必须小心，以避免性能下降。重构后需要进行基准测试，与旧版本进行比较。
- **复杂性**: 新的结构虽然更清晰，但组件数量增多。需要编写全面的单元测试，确保各模块之间的交互正确无误。
- **工作量**: 这是一个彻底的重构，工作量较大。可以分阶段进行，每个阶段完成后都确保所有测试通过，从而小步快跑，降低风险。

这个计划将引导`chesskit-swift`走向一个更健康、更可持续的架构。
